class ScreenController < ApplicationController
  def index
    render layout: "screen/index"
  end

  def get_data
    render json: JSON.parse(SystemDatum.first.data || {})
  end

  def get_screen_data
    # 获取系统资源数据
    data = {
      nodes: {
        total: 12,
        used: 10,
        percent: 85,
        free: 2
      },
      cpu: {
        total: 96,
        used: 86,
        percent: 90,
        free: 10
      },
      gpu: {
        total: 2,
        used: 1,
        percent: 45,
        free: 1
      },
      ram: {
        total: "16G",
        used: "11.5G",
        percent: 72,
        free: "4.5G"
      },
      disk: {
        total: "500G",
        used: "150G",
        percent: 30,
        free: "350G"
      }
    }

    render json: data
  end
end
